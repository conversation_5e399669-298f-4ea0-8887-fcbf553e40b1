import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Button, Card, Row, Col, Space, Table, message, Tabs, InputNumber, TimePicker } from 'antd';
import dayjs from 'dayjs';
import type { TabsProps } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import type { TaskBasic, TaskAlert, DBConnection, AlertSend, OtherInfo, TaskBasicFormDataAdd, TaskBasicFormDataUpdateOrDelete } from '../types/task';
import { WEEKDAY_OPTIONS } from '../types/task';
import { TaskService } from '../services/taskService';
import { AlertModal, DbConnectionModal, AlertSendModal } from './TaskFormModals';
import { OtherInfoModal, SelectModal } from './TaskFormModalsExtended';
import styles from './ComplexTaskForm.module.css';

const { Option } = Select;

interface ComplexTaskFormProps {
  initialData?: TaskBasic;
  onSubmit?: () => void; // 修改为可选的成功回调
  onCancel?: () => void;
  onReset?: () => void;
  isEdit?: boolean;
}

// 频率单位映射
const FREQUENCY_UNIT_MAP = {
  // 英文到中文
  sec: '秒',
  min: '分',
  hour: '时',
  day: '日',
  // 中文到英文
  秒: 'sec',
  分: 'min',
  时: 'hour',
  日: 'day',
} as const;

// 重试频率单位映射
const RETRY_FREQUENCY_UNIT_MAP = {
  // 英文到中文
  sec: '秒',
  min: '分钟',
  hour: '小时',
  // 中文到英文
  秒: 'sec',
  分钟: 'min',
  小时: 'hour',
} as const;

/**
 * 将英文格式的频率字符串转换为中文对象格式
 * @param frequencyStr 格式如 "40sec"、"5hour" 等
 * @param isRetryFrequency 是否为重试频率（影响单位映射）
 * @returns 包含 value 和 unit 的对象
 */
const parseFrequencyFromString = (frequencyStr: string, isRetryFrequency: boolean = false): { value: number; unit: string } | null => {
  const match = frequencyStr.match(/^(\d+)(sec|min|hour|day)$/);
  if (!match) return null;

  const [, value, unit] = match;
  const unitMap = isRetryFrequency ? RETRY_FREQUENCY_UNIT_MAP : FREQUENCY_UNIT_MAP;
  const chineseUnit = unitMap[unit as keyof typeof unitMap];

  return {
    value: parseInt(value),
    unit: chineseUnit || (isRetryFrequency ? '分钟' : '分'),
  };
};

/**
 * 将中文对象格式的频率转换为英文字符串格式
 * @param frequency 包含 value 和 unit 的对象
 * @param isRetryFrequency 是否为重试频率（影响单位映射）
 * @returns 格式如 "40sec"、"5hour" 等的字符串
 */
const formatFrequencyToString = (frequency: { value: number; unit: string }, isRetryFrequency: boolean = false): string => {
  const { value, unit } = frequency;
  const unitMap = isRetryFrequency ? RETRY_FREQUENCY_UNIT_MAP : FREQUENCY_UNIT_MAP;
  const englishUnit = unitMap[unit as keyof typeof unitMap];

  return `${value}${englishUnit || (isRetryFrequency ? 'min' : 'min')}`;
};

// 抽屉表单
const ComplexTaskForm: React.FC<ComplexTaskFormProps> = ({ initialData, onSubmit, onCancel, onReset }) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('basic');
  const [submitLoading, setSubmitLoading] = useState(false);

  // 是否为编辑模式
  const [isEditMode, setEditMode] = useState(false);

  // 各种数据状态
  const [alerts, setAlerts] = useState<TaskAlert[]>([]);
  const [alertSends, setAlertSends] = useState<AlertSend[]>([]);
  const [dbConnection, setDbConnection] = useState<DBConnection | null>(null);
  const [otherInfo, setOtherInfo] = useState<OtherInfo | null>(null);

  // 可选择的数据
  const [availableAlerts, setAvailableAlerts] = useState<TaskAlert[]>([]);
  const [availableDbConnections, setAvailableDbConnections] = useState<DBConnection[]>([]);
  const [availableAlertSends, setAvailableAlertSends] = useState<AlertSend[]>([]);
  const [availableOtherInfos, setAvailableOtherInfos] = useState<OtherInfo[]>([]);

  // Modal状态
  const [alertModal, setAlertModal] = useState({
    visible: false,
    editingIndex: -1,
  });

  // 告警发送Modal
  const [alertSendModal, setAlertSendModal] = useState({
    visible: false,
    editingIndex: -1,
  });

  // 数据库连接Modal
  const [dbConnectionModal, setDbConnectionModal] = useState({
    visible: false,
  });

  // 其他信息Modal
  const [otherInfoModal, setOtherInfoModal] = useState({ visible: false });

  // 选择已有数据Modal
  const [selectModal, setSelectModal] = useState({
    visible: false,
    type: '' as 'alert' | 'alertSend' | 'dbConnection' | 'otherInfo',
  });

  // 加载可选择的数据
  useEffect(() => {
    const loadData = async () => {
      try {
        const [alertsData, dbConnectionsData, alertSendsData, otherInfosData] = await Promise.all([
          TaskService.getAlerts(),
          TaskService.getDbConnections(),
          TaskService.getAlertSends(),
          TaskService.getOtherInfos(),
        ]);

        setAvailableAlerts(alertsData);
        setAvailableDbConnections(dbConnectionsData);
        setAvailableAlertSends(alertSendsData);
        setAvailableOtherInfos(otherInfosData);
      } catch (error) {
        message.error('加载数据失败:' + error);
      }
    };

    loadData();
  }, [initialData]);

  // 初始化表单数据
  useEffect(() => {
    const initializeFormData = async () => {
      if (initialData) {
        // 编辑模式
        setEditMode(true);

        // 处理执行频率和重试频率
        const taskExec = {
          ...initialData,
        };

        // 处理执行频率 - 解析格式如 "40sec"、"5hour" 等
        if (taskExec.frequency && typeof taskExec.frequency === 'string') {
          const parsedFrequency = parseFrequencyFromString(taskExec.frequency, false);
          if (parsedFrequency) {
            taskExec.frequency = parsedFrequency;
          }
        }

        // 处理重试频率 - 解析格式如 "40sec"、"5hour" 等
        if (taskExec.retry_frequency && typeof taskExec.retry_frequency === 'string') {
          const parsedRetryFrequency = parseFrequencyFromString(taskExec.retry_frequency, true);
          if (parsedRetryFrequency) {
            taskExec.retry_frequency = parsedRetryFrequency;
          }
        }

        form.setFieldsValue(taskExec);

        // 加载关联数据
        try {
          // 加载告警数据
          if (initialData.alert_task_id && initialData.alert_task_id.length > 0) {
            try {
              const alertsData = await TaskService.getAlertsByIds(initialData.alert_task_id);
              setAlerts(alertsData);
            } catch (error) {
              console.error('获取告警数据失败:', error);
            }
          }

          // 加载告警发送数据
          if (initialData.alert_send_id && initialData.alert_send_id.length > 0) {
            try {
              const alertSendsData = await TaskService.getAlertSendsByIds(initialData.alert_send_id);
              setAlertSends(alertSendsData);
            } catch (error) {
              console.error('获取告警发送数据失败:', error);
            }
          }

          // 加载数据库连接数据
          if (initialData.db_connection_id) {
            try {
              const dbConnectionData = await TaskService.getDbConnectionById(initialData.db_connection_id);
              setDbConnection(dbConnectionData);
            } catch (error) {
              console.error('获取数据库连接数据失败:', error);
            }
          }

          // 加载其他信息数据
          if (initialData.other_info_id) {
            try {
              const otherInfoData = await TaskService.getOtherInfoById(initialData.other_info_id);
              setOtherInfo(otherInfoData);
            } catch (error) {
              console.error('获取其他信息数据失败:', error);
            }
          }
        } catch (error) {
          console.error('加载关联数据失败:', error);
        }
      } else {
        // 新增模式
        setEditMode(false);

        // 新增模式时清空表单
        form.resetFields();
        setAlerts([]);
        setAlertSends([]);
        setDbConnection(null);
        setOtherInfo(null);
      }
    };

    initializeFormData();
  }, [initialData, form]);

  // 提交表单
  const handleSubmit = async (values: TaskBasic) => {
    try {
      setSubmitLoading(true);
      console.log('表单提交数据:', values);

      // 处理表单数据，转换回原始格式
      const processedValues = {
        ...values,
      };

      // 处理执行频率 - 转换为 "40sec"、"5hour" 等格式
      if (values.frequency && typeof values.frequency === 'object') {
        const frequency = values.frequency as { value: number; unit: string };
        processedValues.frequency = formatFrequencyToString(frequency, false);
      }

      // 处理重试频率 - 转换为 "40sec"、"5hour" 等格式
      if (values.retry_frequency && typeof values.retry_frequency === 'object') {
        const retryFrequency = values.retry_frequency as {
          value: number;
          unit: string;
        };
        processedValues.retry_frequency = formatFrequencyToString(retryFrequency, true);
      }

      // 根据是否有ID区分新增和更新
      if (isEditMode && initialData?.id) {
        // 更新操作 - 使用TaskBasicFormDataUpdateOrDelete类型
        const updateData: TaskBasicFormDataUpdateOrDelete = {
          id: initialData.id,
          name: processedValues.name,
          group: processedValues.group,
          status: processedValues.status,
          start_time: processedValues.start_time,
          end_time: processedValues.end_time,
          weekday: Array.isArray(processedValues.weekday) ? processedValues.weekday.join(',') : processedValues.weekday,
          frequency: typeof processedValues.frequency === 'string' ? processedValues.frequency : formatFrequencyToString(processedValues.frequency, false),
          retry_num: processedValues.retry_num,
          retry_frequency: typeof processedValues.retry_frequency === 'string' ? processedValues.retry_frequency : formatFrequencyToString(processedValues.retry_frequency, true),
          alert_task_id: alerts.map(alert => `alert_${alert.id}`).join(','),
          alert_send_id: alertSends.map(send => `send_${send.id}`).join(','),
          db_connection_id: dbConnection ? `db_${dbConnection.id}` : '',
          other_info_id: otherInfo ? `info_${otherInfo.id}` : '',
        };

        await TaskService.updateComplexForm(initialData.id, updateData);
        message.success('更新成功');
      } else {
        // 新增操作 - 使用TaskBasicFormDataAdd类型
        const addData: TaskBasicFormDataAdd = {
          name: processedValues.name,
          group: processedValues.group,
          status: processedValues.status,
          start_time: processedValues.start_time,
          end_time: processedValues.end_time,
          weekday: Array.isArray(processedValues.weekday) ? processedValues.weekday.join(',') : processedValues.weekday,
          frequency: typeof processedValues.frequency === 'string' ? processedValues.frequency : formatFrequencyToString(processedValues.frequency, false),
          retry_num: processedValues.retry_num,
          retry_frequency: typeof processedValues.retry_frequency === 'string' ? processedValues.retry_frequency : formatFrequencyToString(processedValues.retry_frequency, true),
          alert_task_id: alerts.map(alert => `alert_${alert.id}`).join(','),
          alert_send_id: alertSends.map(send => `send_${send.id}`).join(','),
          db_connection_id: dbConnection ? `db_${dbConnection.id}` : '',
          other_info_id: otherInfo ? `info_${otherInfo.id}` : '',
        };

        await TaskService.saveComplexForm(addData);
        message.success('创建成功');
      }

      // 调用成功回调
      if (onSubmit) {
        onSubmit();
      }
    } catch (error) {
      message.error(isEditMode ? '更新失败' : '创建失败');
      console.error('提交失败:', error);
    } finally {
      setSubmitLoading(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    setAlerts([]);
    setAlertSends([]);
    setDbConnection(null);
    setOtherInfo(null);
    if (onReset) {
      onReset();
    }
  };

  // 取消操作
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  // 添加告警
  const handleAddAlert = (alert: TaskAlert) => {
    if (alertModal.editingIndex >= 0) {
      const newAlerts = [...alerts];
      newAlerts[alertModal.editingIndex] = alert;
      setAlerts(newAlerts);
    } else {
      setAlerts([...alerts, { ...alert, id: Date.now() }]);
    }
    setAlertModal({
      visible: false,
      editingIndex: -1,
    });
  };

  // 删除告警
  const handleDeleteAlert = (index: number) => {
    const newAlerts = alerts.filter((_, i) => i !== index);
    setAlerts(newAlerts);
  };

  // 添加告警发送
  const handleAddAlertSend = (alertSend: AlertSend) => {
    if (alertSendModal.editingIndex >= 0) {
      const newAlertSends = [...alertSends];
      newAlertSends[alertSendModal.editingIndex] = alertSend;
      setAlertSends(newAlertSends);
    } else {
      setAlertSends([
        ...alertSends,
        {
          ...alertSend,
          id: Date.now(),
        },
      ]);
    }
    setAlertSendModal({
      visible: false,
      editingIndex: -1,
    });
  };

  // 删除告警发送
  const handleDeleteAlertSend = (index: number) => {
    const newAlertSends = alertSends.filter((_, i) => i !== index);
    setAlertSends(newAlertSends);
  };

  // 选择已有数据
  const handleSelectExisting = (type: string, selectedItems: TaskAlert[] | AlertSend[] | DBConnection[] | OtherInfo[]) => {
    switch (type) {
      case 'alert': {
        // 过滤掉已经存在的告警，避免重复添加
        const alertItems = selectedItems as TaskAlert[];
        const existingAlertIds = alerts.map(alert => alert.id);
        const newAlerts = alertItems.filter(item => !existingAlertIds.includes(item.id));
        if (newAlerts.length > 0) {
          setAlerts([...alerts, ...newAlerts]);
          message.success(`成功添加 ${newAlerts.length} 个告警`);
        } else {
          message.warning('所选告警已存在，未添加任何新项');
        }
        break;
      }
      case 'alertSend': {
        // 过滤掉已经存在的告警发送，避免重复添加
        const alertSendItems = selectedItems as AlertSend[];
        const existingAlertSendIds = alertSends.map(send => send.id);
        const newAlertSends = alertSendItems.filter(item => !existingAlertSendIds.includes(item.id));
        if (newAlertSends.length > 0) {
          setAlertSends([...alertSends, ...newAlertSends]);
          message.success(`成功添加 ${newAlertSends.length} 个告警发送`);
        } else {
          message.warning('所选告警发送已存在，未添加任何新项');
        }
        break;
      }
      case 'dbConnection': {
        const dbConnectionItems = selectedItems as DBConnection[];
        if (dbConnectionItems.length > 0) {
          setDbConnection(dbConnectionItems[0]);
          message.success('成功设置数据库连接');
        }
        break;
      }
      case 'otherInfo': {
        const otherInfoItems = selectedItems as OtherInfo[];
        if (otherInfoItems.length > 0) {
          setOtherInfo(otherInfoItems[0]);
          message.success('成功设置其他信息');
        }
        break;
      }
    }
    setSelectModal({
      visible: false,
      type: 'alert',
    });
  };

  // 定义Tabs的items配置
  const tabItems: TabsProps['items'] = [
    {
      key: 'basic',
      label: '基本信息',
      children: (
        <Card title='任务执行配置' className='mb-4'>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label='任务名称'
                name='name'
                rules={[
                  {
                    required: true,
                    message: '请输入任务名称',
                  },
                ]}
              >
                <Input placeholder='请输入任务名称' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label='任务分组'
                name='group'
                rules={[
                  {
                    required: true,
                    message: '请输入任务分组',
                  },
                ]}
              >
                <Input placeholder='请输入任务分组' />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label='开始时间'
                name='start_time'
                rules={[
                  {
                    required: true,
                    message: '请选择开始时间',
                  },
                ]}
                getValueFromEvent={time => (time ? time.format('HH:mm:ss') : '')}
                getValueProps={value => ({
                  value: value ? (typeof value === 'string' ? dayjs(value, 'HH:mm:ss') : value) : undefined,
                })}
              >
                <TimePicker
                  placeholder='请选择开始时间'
                  format='HH:mm:ss'
                  style={{
                    width: '100%',
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label='结束时间'
                name='end_time'
                rules={[
                  {
                    required: true,
                    message: '请选择结束时间',
                  },
                ]}
                getValueFromEvent={time => (time ? time.format('HH:mm:ss') : '')}
                getValueProps={value => ({
                  value: value ? (typeof value === 'string' ? dayjs(value, 'HH:mm:ss') : value) : undefined,
                })}
              >
                <TimePicker
                  placeholder='请选择结束时间'
                  format='HH:mm:ss'
                  style={{
                    width: '100%',
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label='状态'
                name='status'
                rules={[
                  {
                    required: true,
                    message: '请选择状态',
                  },
                ]}
              >
                <Select placeholder='请选择状态'>
                  <Option value='enabled'>启用</Option>
                  <Option value='disabled'>禁用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label='星期'
                name='weekday'
                rules={[
                  {
                    required: true,
                    message: '请选择星期',
                  },
                ]}
              >
                <Select mode='multiple' placeholder='请选择星期' allowClear>
                  {WEEKDAY_OPTIONS.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label='执行频率'
                rules={[
                  {
                    required: true,
                    message: '请输入执行频率',
                  },
                ]}
              >
                <Space.Compact
                  style={{
                    width: '100%',
                  }}
                >
                  <Form.Item
                    name={['frequency', 'value']}
                    noStyle
                    rules={[
                      {
                        required: true,
                        message: '请输入频率值',
                      },
                    ]}
                  >
                    <InputNumber
                      placeholder='请输入频率值'
                      style={{
                        width: '70%',
                      }}
                      min={1}
                    />
                  </Form.Item>
                  <Form.Item
                    name={['frequency', 'unit']}
                    noStyle
                    rules={[
                      {
                        required: true,
                        message: '请选择单位',
                      },
                    ]}
                  >
                    <Select
                      placeholder='单位'
                      style={{
                        width: '30%',
                      }}
                    >
                      <Option value='秒'>秒</Option>
                      <Option value='分'>分</Option>
                      <Option value='时'>时</Option>
                      <Option value='日'>日</Option>
                    </Select>
                  </Form.Item>
                </Space.Compact>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label='重试次数'
                name='retry_num'
                rules={[
                  {
                    required: true,
                    message: '请输入重试次数',
                  },
                ]}
              >
                <Input placeholder='请输入重试次数' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label='重试间隔'
                rules={[
                  {
                    required: true,
                    message: '请输入重试间隔',
                  },
                ]}
              >
                <Space.Compact
                  style={{
                    width: '100%',
                  }}
                >
                  <Form.Item
                    name={['retry_frequency', 'value']}
                    noStyle
                    rules={[
                      {
                        required: true,
                        message: '请输入间隔值',
                      },
                    ]}
                  >
                    <InputNumber
                      placeholder='请输入间隔值'
                      style={{
                        width: '70%',
                      }}
                      min={1}
                    />
                  </Form.Item>
                  <Form.Item
                    name={['retry_frequency', 'unit']}
                    noStyle
                    rules={[
                      {
                        required: true,
                        message: '请选择单位',
                      },
                    ]}
                  >
                    <Select
                      placeholder='单位'
                      style={{
                        width: '30%',
                      }}
                    >
                      <Option value='秒'>秒</Option>
                      <Option value='分钟'>分钟</Option>
                      <Option value='小时'>小时</Option>
                    </Select>
                  </Form.Item>
                </Space.Compact>
              </Form.Item>
            </Col>
          </Row>
        </Card>
      ),
    },
    {
      key: 'alerts',
      label: '告警配置',
      children: (
        <Card
          title='告警规则'
          extra={
            <Space>
              <Button
                type='primary'
                icon={<PlusOutlined />}
                onClick={() =>
                  setAlertModal({
                    visible: true,
                    editingIndex: -1,
                  })
                }
              >
                新增告警
              </Button>
              <Button
                icon={<EyeOutlined />}
                onClick={() =>
                  setSelectModal({
                    visible: true,
                    type: 'alert',
                  })
                }
              >
                选择告警
              </Button>
              {alerts.length > 0 && (
                <Button
                  danger
                  onClick={() => {
                    setAlerts([]);
                    message.success('已清除所有告警配置');
                  }}
                >
                  清除选择
                </Button>
              )}
            </Space>
          }
        >
          <Table
            dataSource={alerts}
            rowKey='id'
            pagination={false}
            columns={[
              {
                title: '告警名称',
                dataIndex: 'name',
                key: 'name',
                ellipsis: true,
              },
              {
                title: '告警级别',
                dataIndex: 'severity',
                key: 'severity',
                ellipsis: true,
              },
              {
                title: '告警类型',
                dataIndex: 'type',
                key: 'type',
                ellipsis: true,
              },
              {
                title: '创建日期',
                dataIndex: 'create_time',
                key: 'create_time',
                ellipsis: true,
              },
              {
                title: '更新日期',
                dataIndex: 'update_time',
                key: 'update_time',
                ellipsis: true,
              },
              {
                title: 'SQL语句',
                dataIndex: 'sql',
                key: 'sql',
                ellipsis: true,
              },
              {
                title: '操作',
                key: 'action',
                render: (_, _record, index) => (
                  <Space>
                    <Button
                      type='text'
                      icon={<EditOutlined />}
                      onClick={() =>
                        setAlertModal({
                          visible: true,
                          editingIndex: index,
                        })
                      }
                    >
                      编辑
                    </Button>
                    <Button type='text' danger icon={<DeleteOutlined />} onClick={() => handleDeleteAlert(index)}>
                      删除
                    </Button>
                  </Space>
                ),
              },
            ]}
          />
        </Card>
      ),
    },
    {
      key: 'database',
      label: '数据库连接',
      children: (
        <Card
          title='数据库连接配置'
          extra={
            <Space>
              <Button
                type='primary'
                icon={<PlusOutlined />}
                onClick={() =>
                  setDbConnectionModal({
                    visible: true,
                  })
                }
              >
                新增连接
              </Button>
              <Button
                icon={<EyeOutlined />}
                onClick={() =>
                  setSelectModal({
                    visible: true,
                    type: 'dbConnection',
                  })
                }
              >
                选择连接
              </Button>
              {dbConnection && (
                <Button
                  danger
                  onClick={() => {
                    setDbConnection(null);
                    message.success('已清除数据库连接配置');
                  }}
                >
                  清除选择
                </Button>
              )}
            </Space>
          }
        >
          {dbConnection ? (
            <div className='p-4 border border-gray-200 rounded'>
              <Row gutter={16}>
                <Col span={8}>
                  <strong>连接名称：</strong>
                  {dbConnection.name}
                </Col>
                <Col span={8}>
                  <strong>数据库类型：</strong>
                  {dbConnection.db_type}
                </Col>
                <Col span={8}>
                  <strong>主机地址：</strong>
                  {dbConnection.host}:{dbConnection.port}
                </Col>
              </Row>
              <div className='mt-2'>
                <Button type='link' icon={<EditOutlined />} onClick={() => setDbConnectionModal({ visible: true })}>
                  编辑
                </Button>
                <Button type='link' danger onClick={() => setDbConnection(null)}>
                  删除
                </Button>
              </div>
            </div>
          ) : (
            <div className='text-center text-gray-500 py-8'>暂无数据库连接配置</div>
          )}
        </Card>
      ),
    },
    {
      key: 'alertSend',
      label: '告警发送',
      children: (
        <Card
          title='告警发送配置'
          extra={
            <Space>
              <Button
                type='primary'
                icon={<PlusOutlined />}
                onClick={() =>
                  setAlertSendModal({
                    visible: true,
                    editingIndex: -1,
                  })
                }
              >
                新增发送方式
              </Button>
              <Button
                icon={<EyeOutlined />}
                onClick={() =>
                  setSelectModal({
                    visible: true,
                    type: 'alertSend',
                  })
                }
              >
                选择联系方式
              </Button>
              {alertSends.length > 0 && (
                <Button
                  danger
                  onClick={() => {
                    setAlertSends([]);
                    message.success('已清除所有告警发送配置');
                  }}
                >
                  清除选择
                </Button>
              )}
            </Space>
          }
        >
          {alertSends.length > 0 ? (
            <div className='space-y-4'>
              {alertSends.map((alertSend, index) => (
                <div key={alertSend.id} className='p-4 border border-gray-200 rounded'>
                  <Row gutter={16}>
                    <Col span={8}>
                      <strong>发送名称：</strong>
                      {alertSend.name}
                    </Col>
                    <Col span={8}>
                      <strong>发送类型：</strong>
                      {alertSend.type === 'kafka' ? 'Kafka' : 'Prometheus'}
                    </Col>
                    <Col span={8}>
                      <strong>创建时间：</strong>
                      {alertSend.create_time}
                    </Col>
                  </Row>

                  {alertSend.type === 'kafka' && alertSend.kafka_receiver.address && (
                    <Row gutter={16} className='mt-2'>
                      <Col span={8}>
                        <strong>Kafka地址：</strong>
                        {alertSend.kafka_receiver.address}
                      </Col>
                      <Col span={8}>
                        <strong>用户名：</strong>
                        {alertSend.kafka_receiver.username}
                      </Col>
                      <Col span={8}>
                        <strong>Topic：</strong>
                        {alertSend.kafka_receiver.topic}
                      </Col>
                    </Row>
                  )}

                  {alertSend.type === 'prometheus' && alertSend.prometheus_receiver.address && (
                    <Row gutter={16} className='mt-2'>
                      <Col span={8}>
                        <strong>Prometheus地址：</strong>
                        {alertSend.prometheus_receiver.address}
                      </Col>
                      <Col span={8}>
                        <strong>用户名：</strong>
                        {alertSend.prometheus_receiver.username}
                      </Col>
                    </Row>
                  )}

                  <div className='mt-2'>
                    <Button
                      type='link'
                      icon={<EditOutlined />}
                      onClick={() =>
                        setAlertSendModal({
                          visible: true,
                          editingIndex: index,
                        })
                      }
                    >
                      编辑
                    </Button>
                    <Button type='link' danger onClick={() => handleDeleteAlertSend(index)}>
                      删除
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className='text-center text-gray-500 py-8'>暂无告警发送配置</div>
          )}
        </Card>
      ),
    },
    {
      key: 'otherInfo',
      label: '其他信息',
      children: (
        <Card
          title='其他信息配置'
          extra={
            <Space>
              <Button
                type='primary'
                icon={<PlusOutlined />}
                onClick={() =>
                  setOtherInfoModal({
                    visible: true,
                  })
                }
              >
                新增信息
              </Button>
              <Button
                icon={<EyeOutlined />}
                onClick={() =>
                  setSelectModal({
                    visible: true,
                    type: 'otherInfo',
                  })
                }
              >
                选择附加信息
              </Button>
              {otherInfo && (
                <Button
                  danger
                  onClick={() => {
                    setOtherInfo(null);
                    message.success('已清除其他信息配置');
                  }}
                >
                  清除选择
                </Button>
              )}
            </Space>
          }
        >
          {otherInfo ? (
            <div className='p-4 border border-gray-200 rounded'>
              <Row gutter={16}>
                <Col span={8}>
                  <strong>信息名称：</strong>
                  {otherInfo.name}
                </Col>
                <Col span={8}>
                  <strong>业务系统：</strong>
                  {otherInfo.business}
                </Col>
                <Col span={8}>
                  <strong>主机名称：</strong>
                  {otherInfo.hostname}
                </Col>
              </Row>
              <Row gutter={16} className='mt-2'>
                <Col span={8}>
                  <strong>英文名称：</strong>
                  {otherInfo.business_en}
                </Col>
                <Col span={8}>
                  <strong>告警来源：</strong>
                  {otherInfo.location}
                </Col>
              </Row>
              <div className='mt-2'>
                <Button
                  type='link'
                  icon={<EditOutlined />}
                  onClick={() =>
                    setOtherInfoModal({
                      visible: true,
                    })
                  }
                >
                  编辑
                </Button>
                <Button type='link' danger onClick={() => setOtherInfo(null)}>
                  删除
                </Button>
              </div>
            </div>
          ) : (
            <div className='text-center text-gray-500 py-8'>暂无其他信息配置</div>
          )}
        </Card>
      ),
    },
  ];

  // console.log(tabItems);

  return (
    <div className='flex flex-col h-full'>
      <div className='flex-1 overflow-auto'>
        <Form form={form} layout='vertical' onFinish={handleSubmit}>
          <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} />
        </Form>
      </div>

      {/* 底部操作栏 */}
      <div className={`flex-shrink-0 ${styles.footerContainer}`}>
        <div className='flex justify-between items-center'>
          {/* 左侧提示信息 */}
          <div className={styles.footerHint}>{isEditMode ? '编辑任务配置' : '新建任务配置'}</div>

          {/* 右侧按钮组 */}
          <div className={styles.buttonGroup}>
            {isEditMode ? (
              // 编辑模式：取消/确认
              <>
                <Button onClick={handleCancel} size='large' className={`${styles.actionButton} ${styles.cancelButton}`}>
                  取消
                </Button>
                <Button
                  type='primary'
                  onClick={() => form.submit()}
                  loading={submitLoading}
                  size='large'
                  className={`${styles.actionButton} ${styles.confirmButton} ${submitLoading ? styles.loadingButton : ''}`}
                >
                  {submitLoading ? '保存中...' : '确认修改'}
                </Button>
              </>
            ) : (
              // 新增模式：取消/重置/提交
              <>
                <Button onClick={handleCancel} size='large' className={`${styles.actionButton} ${styles.cancelButton}`}>
                  取消
                </Button>
                <Button onClick={handleReset} size='large' className={`${styles.actionButton} ${styles.resetButton}`}>
                  重置表单
                </Button>
                <Button
                  type='primary'
                  onClick={() => form.submit()}
                  loading={submitLoading}
                  size='large'
                  className={`${styles.actionButton} ${styles.submitButton} ${submitLoading ? styles.loadingButton : ''}`}
                >
                  {submitLoading ? '创建中...' : '创建任务'}
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* 各种Modal组件 */}
      <AlertModal
        visible={alertModal.visible}
        editingData={alertModal.editingIndex >= 0 ? alerts[alertModal.editingIndex] : undefined}
        onCancel={() =>
          setAlertModal({
            visible: false,
            editingIndex: -1,
          })
        }
        onSubmit={handleAddAlert}
      />

      <DbConnectionModal
        visible={dbConnectionModal.visible}
        editingData={dbConnection || undefined}
        onCancel={() =>
          setDbConnectionModal({
            visible: false,
          })
        }
        onSubmit={data => {
          setDbConnection(data);
          setDbConnectionModal({
            visible: false,
          });
        }}
      />

      <AlertSendModal
        visible={alertSendModal.visible}
        editingData={alertSendModal.editingIndex >= 0 ? alertSends[alertSendModal.editingIndex] : undefined}
        onCancel={() =>
          setAlertSendModal({
            visible: false,
            editingIndex: -1,
          })
        }
        onSubmit={handleAddAlertSend}
      />

      <OtherInfoModal
        visible={otherInfoModal.visible}
        editingData={otherInfo || undefined}
        onCancel={() =>
          setOtherInfoModal({
            visible: false,
          })
        }
        onSubmit={data => {
          setOtherInfo(data);
          setOtherInfoModal({
            visible: false,
          });
        }}
      />

      <SelectModal
        visible={selectModal.visible}
        type={selectModal.type}
        data={
          selectModal.type === 'alert'
            ? availableAlerts
            : selectModal.type === 'alertSend'
              ? availableAlertSends
              : selectModal.type === 'dbConnection'
                ? availableDbConnections
                : selectModal.type === 'otherInfo'
                  ? availableOtherInfos
                  : []
        }
        selectedData={
          selectModal.type === 'alert'
            ? alerts
            : selectModal.type === 'alertSend'
              ? alertSends
              : selectModal.type === 'dbConnection'
                ? dbConnection
                  ? [dbConnection]
                  : []
                : selectModal.type === 'otherInfo'
                  ? otherInfo
                    ? [otherInfo]
                    : []
                  : []
        }
        multiple={selectModal.type !== 'dbConnection' && selectModal.type !== 'otherInfo'}
        onCancel={() =>
          setSelectModal({
            visible: false,
            type: 'alert',
          })
        }
        onSubmit={selectedItems => handleSelectExisting(selectModal.type, selectedItems)}
        onSearch={searchText => {
          // 根据不同类型调用不同的搜索API
          switch (selectModal.type) {
            case 'alert':
              console.log('搜索告警:', searchText);
              // TODO: TaskService.searchAlerts(searchText).then(setAvailableAlerts);
              break;
            case 'dbConnection':
              if (searchText) {
                try {
                  const searchFields = JSON.parse(searchText);
                  console.log('搜索数据库连接:', searchFields);
                  // TODO: TaskService.searchDbConnections(searchFields).then(setAvailableDbConnections);
                } catch (e) {
                  console.log('重置数据库连接搜索' + e);
                  // TODO: 重置搜索结果
                }
              } else {
                console.log('重置数据库连接搜索');
                // TODO: 重置搜索结果
              }
              break;
            case 'alertSend':
              if (searchText) {
                try {
                  const searchFields = JSON.parse(searchText);
                  console.log('搜索告警发送:', searchFields);
                  // TODO: TaskService.searchAlertSends(searchFields).then(setAvailableAlertSends);
                } catch (e) {
                  console.log('重置告警发送搜索' + e);
                  // TODO: 重置搜索结果
                }
              } else {
                console.log('重置告警发送搜索');
                // TODO: 重置搜索结果
              }
              break;
            case 'otherInfo':
              console.log('搜索其他信息:', searchText);
              // TODO: TaskService.searchOtherInfos(searchText).then(setAvailableOtherInfos);
              break;
          }
        }}
      />
    </div>
  );
};

export default ComplexTaskForm;
