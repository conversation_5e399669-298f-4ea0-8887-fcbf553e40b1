import React from 'react';
import { Card, Row, Col, Statistic, Progress, List, Avatar, Tag } from 'antd';
import { UserOutlined, ShoppingCartOutlined, DollarOutlined, RiseOutlined, FallOutlined } from '@ant-design/icons';

const DashboardPage: React.FC = () => {
  // 模拟数据
  const statisticsData = [
    {
      title: '总用户数',
      value: 11280,
      prefix: <UserOutlined />,
      valueStyle: { color: '#3f8600' },
      suffix: <RiseOutlined style={{ color: '#3f8600' }} />,
    },
    {
      title: '总订单数',
      value: 8846,
      prefix: <ShoppingCartOutlined />,
      valueStyle: { color: '#1890ff' },
    },
    {
      title: '总收入',
      value: 9280,
      prefix: <DollarOutlined />,
      valueStyle: { color: '#cf1322' },
      suffix: <FallOutlined style={{ color: '#cf1322' }} />,
    },
    {
      title: '转化率',
      value: 93.5,
      precision: 1,
      suffix: '%',
      valueStyle: { color: '#3f8600' },
    },
  ];

  const recentActivities = [
    {
      title: '用户张三完成了订单支付',
      description: '订单号：#12345',
      avatar: <Avatar icon={<UserOutlined />} />,
      time: '2分钟前',
    },
    {
      title: '新用户李四注册成功',
      description: '来源：微信小程序',
      avatar: (
        <Avatar
          style={{
            backgroundColor: '#87d068',
          }}
          icon={<UserOutlined />}
        />
      ),
      time: '5分钟前',
    },
    {
      title: '系统自动备份完成',
      description: '数据库备份成功',
      avatar: (
        <Avatar
          style={{
            backgroundColor: '#108ee9',
          }}
        >
          系
        </Avatar>
      ),
      time: '10分钟前',
    },
    {
      title: '用户王五申请退款',
      description: '订单号：#12344',
      avatar: (
        <Avatar
          style={{
            backgroundColor: '#f56a00',
          }}
          icon={<UserOutlined />}
        />
      ),
      time: '15分钟前',
    },
  ];

  return (
    <div>
      <h1
        style={{
          marginBottom: 24,
          fontSize: 24,
          fontWeight: 600,
        }}
      >
        仪表板
      </h1>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {statisticsData.map((item, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic title={item.title} value={item.value} precision={item.precision} valueStyle={item.valueStyle} prefix={item.prefix} suffix={item.suffix} />
            </Card>
          </Col>
        ))}
      </Row>

      <Row gutter={[16, 16]}>
        {/* 项目进度 */}
        <Col xs={24} lg={12}>
          <Card title='项目进度' style={{ height: 400 }}>
            <div
              style={{
                marginBottom: 16,
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: 8,
                }}
              >
                <span>React 项目重构</span>
                <span>75%</span>
              </div>
              <Progress percent={75} status='active' />
            </div>

            <div
              style={{
                marginBottom: 16,
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: 8,
                }}
              >
                <span>API 接口开发</span>
                <span>60%</span>
              </div>
              <Progress percent={60} />
            </div>

            <div
              style={{
                marginBottom: 16,
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: 8,
                }}
              >
                <span>数据库优化</span>
                <span>90%</span>
              </div>
              <Progress percent={90} status='active' />
            </div>

            <div
              style={{
                marginBottom: 16,
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: 8,
                }}
              >
                <span>测试用例编写</span>
                <span>45%</span>
              </div>
              <Progress percent={45} />
            </div>
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={12}>
          <Card title='最近活动' style={{ height: 400 }}>
            <List
              itemLayout='horizontal'
              dataSource={recentActivities}
              renderItem={item => (
                <List.Item>
                  <List.Item.Meta
                    avatar={item.avatar}
                    title={
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}
                      >
                        <span>{item.title}</span>
                        <Tag color='blue'>{item.time}</Tag>
                      </div>
                    }
                    description={item.description}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DashboardPage;
